<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付成功 - ECR亲密关系经历量表</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .success-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #52c41a, #389e0d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            animation: successPulse 2s ease-in-out infinite;
        }

        .success-icon i {
            font-size: 2.5em;
            color: white;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 15px;
            font-weight: 300;
        }

        .success-message {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .countdown {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            border-left: 4px solid #52c41a;
        }

        .countdown-text {
            color: #2c3e50;
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .countdown-timer {
            font-size: 2em;
            font-weight: bold;
            color: #52c41a;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .success-container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 1.8em;
            }

            .success-icon {
                width: 60px;
                height: 60px;
            }

            .success-icon i {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>
        
        <h1>支付成功！</h1>
        
        <div class="success-message">
            感谢您的支付！我们正在为您准备专业版的详细心理分析报告。
        </div>

        <div class="countdown">
            <div class="countdown-text">
                <span class="loading-spinner"></span>
                正在跳转到报告页面，请稍候...
            </div>
            <div class="countdown-timer" id="countdown">3</div>
        </div>

        <div class="button-group">
            <button class="btn" onclick="redirectToReport()" id="redirectBtn">
                <i class="fas fa-chart-line"></i> 立即查看报告
            </button>
            <a href="index.html" class="btn btn-outline">
                <i class="fas fa-home"></i> 返回首页
            </a>
            <button class="btn btn-outline" onclick="debugInfo()" style="font-size: 0.9em;">
                <i class="fas fa-bug"></i> 调试信息
            </button>
        </div>
    </div>

    <script>
        // 倒计时和自动跳转
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                redirectToReport();
            }
        }, 1000);

        // 跳转到报告页面
        function redirectToReport() {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('session_id');

            console.log('支付成功页面 - 准备跳转');
            console.log('当前URL:', window.location.href);
            console.log('session_id:', sessionId);
            console.log('localStorage pendingResults:', localStorage.getItem('pendingResults'));
            console.log('localStorage ecrBackupResults:', localStorage.getItem('ecrBackupResults'));

            // 更新localStorage中的支付状态为已完成
            const pendingResults = localStorage.getItem('pendingResults');
            const backupResults = localStorage.getItem('ecrBackupResults');

            if (pendingResults) {
                try {
                    const data = JSON.parse(pendingResults);
                    data.paymentStatus = 'completed';
                    data.paymentCompletedAt = Date.now();
                    localStorage.setItem('pendingResults', JSON.stringify(data));
                    console.log('已更新pendingResults支付状态为completed');
                } catch (error) {
                    console.error('更新pendingResults支付状态失败:', error);
                }
            }

            if (backupResults) {
                try {
                    const data = JSON.parse(backupResults);
                    data.paymentStatus = 'completed';
                    data.paymentCompletedAt = Date.now();
                    localStorage.setItem('ecrBackupResults', JSON.stringify(data));
                    console.log('已更新ecrBackupResults支付状态为completed');
                } catch (error) {
                    console.error('更新ecrBackupResults支付状态失败:', error);
                }
            }

            if (sessionId) {
                // 跳转到主页面，主页面会检测支付成功状态
                const targetUrl = `index.html?session_id=${sessionId}`;
                console.log('跳转到:', targetUrl);
                window.location.href = targetUrl;
            } else {
                // 如果没有session_id，直接跳转到主页
                console.log('没有session_id，跳转到首页');
                window.location.href = 'index.html';
            }
        }

        // 调试信息函数
        function debugInfo() {
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('session_id');

            const info = `
调试信息：
- 当前URL: ${window.location.href}
- session_id: ${sessionId || '无'}
- localStorage pendingResults: ${localStorage.getItem('pendingResults') ? '有数据' : '无数据'}
- localStorage ecrBackupResults: ${localStorage.getItem('ecrBackupResults') ? '有数据' : '无数据'}
            `;

            alert(info);
            console.log('=== 支付成功页面调试信息 ===');
            console.log('当前URL:', window.location.href);
            console.log('session_id:', sessionId);
            console.log('localStorage pendingResults:', localStorage.getItem('pendingResults'));
            console.log('localStorage ecrBackupResults:', localStorage.getItem('ecrBackupResults'));
        }

        // 页面加载时的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有session_id参数
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('session_id');

            console.log('支付成功页面加载完成');
            console.log('session_id:', sessionId);

            if (!sessionId) {
                // 如果没有session_id，显示错误信息
                document.querySelector('.success-message').innerHTML =
                    '支付信息验证中，如果长时间未跳转，请手动返回首页查看报告。';
            }
        });
    </script>
</body>
</html>
